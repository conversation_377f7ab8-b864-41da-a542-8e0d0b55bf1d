'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Play, Pause, Download, Trash2, Disc } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRemixModalTheme } from '@/app/remix/utils/modal-theme';
import {
  UploadFile,
  validateFiles,
  createUploadFiles,
  uploadFile as uploadFileUtil,
  formatFileSize,
  formatDuration,
  createAudioUrl,
  cleanupAudioUrl
} from '@/lib/file-upload-utils';
import Image from 'next/image';

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (files: UploadFile[]) => void;
}

export function FileUploadModal({ isOpen, onClose, onSubmit }: FileUploadModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [applicationMessage, setApplicationMessage] = useState('');
  const [title, setTitle] = useState('');
  const [playingFileId, setPlayingFileId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [fileDurations, setFileDurations] = useState<Map<string, number>>(new Map());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get remix theme for modal
  const { cssVariables, themeMode } = useRemixModalTheme();



  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFiles = useCallback((files: File[]) => {
    const { validFiles, errors: validationErrors } = validateFiles(files, uploadFiles);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors([]);
    const newUploadFiles = createUploadFiles(validFiles);
    setUploadFiles(prev => [...prev, ...newUploadFiles]);

    // Start uploading files and get durations
    startUploading(newUploadFiles);
  }, [uploadFiles]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  }, [handleFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
    
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  const startUploading = async (filesToUpload: UploadFile[]) => {
    setIsUploading(true);

    for (const uploadFile of filesToUpload) {
      // Get audio duration
      const audio = new Audio();
      const audioUrl = createAudioUrl(uploadFile.file);
      audio.src = audioUrl;
      
      audio.addEventListener('loadedmetadata', () => {
        setFileDurations(prev => new Map(prev.set(uploadFile.id, audio.duration)));
        cleanupAudioUrl(audioUrl);
      });

      try {
        const result = await uploadFileUtil(uploadFile, (progress: number) => {
          setUploadFiles(prev => 
            prev.map(f => 
              f.id === uploadFile.id 
                ? { ...f, progress }
                : f
            )
          );
        });

        setUploadFiles(prev => 
          prev.map(f => 
            f.id === uploadFile.id 
              ? { 
                  ...f, 
                  status: result.success ? 'completed' : 'error',
                  url: result.url,
                  error: result.error,
                  progress: result.success ? 100 : 0
                }
              : f
          )
        );
      } catch {
        setUploadFiles(prev => 
          prev.map(f => 
            f.id === uploadFile.id 
              ? { 
                  ...f, 
                  status: 'error',
                  error: 'Upload failed',
                  progress: 0
                }
              : f
          )
        );
      }
    }

    setIsUploading(false);
  };

  const handleRemoveFile = useCallback((fileId: string) => {
    // Stop audio if playing
    if (playingFileId === fileId) {
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingFileId(null);
    }
    
    // Remove from state
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
    setFileDurations(prev => {
      const newMap = new Map(prev);
      newMap.delete(fileId);
      return newMap;
    });
    
    // Cleanup audio element
    const audio = audioElements.get(fileId);
    if (audio) {
      const audioUrl = audio.src;
      audio.pause();
      cleanupAudioUrl(audioUrl);
      setAudioElements(prev => {
        const newMap = new Map(prev);
        newMap.delete(fileId);
        return newMap;
      });
    }
  }, [playingFileId, audioElements]);

  const handlePlayPause = useCallback((fileId: string, file: File) => {
    if (playingFileId === fileId) {
      // Pause current file
      const audio = audioElements.get(fileId);
      if (audio) {
        audio.pause();
      }
      setPlayingFileId(null);
    } else {
      // Stop any currently playing audio
      if (playingFileId) {
        const currentAudio = audioElements.get(playingFileId);
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }
      }

      // Play new file
      let audio = audioElements.get(fileId);
      if (!audio) {
        audio = new Audio();
        const audioUrl = createAudioUrl(file);
        audio.src = audioUrl;
        
        audio.addEventListener('ended', () => {
          setPlayingFileId(null);
        });
        
        setAudioElements(prev => new Map(prev.set(fileId, audio!)));
      }
      
      audio.play();
      setPlayingFileId(fileId);
    }
  }, [playingFileId, audioElements]);

  const handleDownload = useCallback((uploadFile: UploadFile) => {
    const url = createAudioUrl(uploadFile.file);
    const link = document.createElement('a');
    link.href = url;
    link.download = uploadFile.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    cleanupAudioUrl(url);
  }, []);

  const handleSubmit = () => {
    const completedFiles = uploadFiles.filter(f => f.status === 'completed');
    if (completedFiles.length > 0) {
      setIsSubmitted(true);
      // Call onSubmit with completed files after a small delay to ensure state update
      setTimeout(() => {
        onSubmit(completedFiles);
      }, 100);
    }
  };

  // handleDone function removed as it's not currently used
  // const handleDone = () => {
  //   const completedFiles = uploadFiles.filter(f => f.status === 'completed');
  //   onSubmit(completedFiles);
  //   handleClose();
  // };

  const handleClose = () => {
    // Cleanup all audio elements
    audioElements.forEach((audio) => {
      audio.pause();
      const audioUrl = audio.src;
      cleanupAudioUrl(audioUrl);
    });

    setUploadFiles([]);
    setErrors([]);
    setIsUploading(false);
    setApplicationMessage('');
    setTitle('');
    setPlayingFileId(null);
    setAudioElements(new Map());
    setFileDurations(new Map());
    setIsSubmitted(false);
    onClose();
  };

  const completedFiles = uploadFiles.filter(f => f.status === 'completed');
  const canSubmit = completedFiles.length > 0 && !isUploading && title.trim().length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="max-w-[calc(100%-2rem)] sm:max-w-2xl h-[90vh] flex flex-col p-0 overflow-hidden"
        style={cssVariables}
        data-theme={themeMode}
      >
        {isSubmitted ? (
          /* Success Screen */
          <div className="flex flex-col items-center justify-center p-6 sm:p-12 text-center space-y-6">
            <div className=" rounded-full flex items-center justify-center">
              {/* <CheckCircle className="h-8 w-8 text-white" /> */}
              <Image
                                                    src="/check.svg"
            alt="check"
                                                    width={150}
                                                    height={150}
                                                    className="object-contain w-full h-full relative z-10"
                                                  />
            </div>
            <div className="space-y-2">
              <h2 className="text-xl font-bold text-foreground">
                Yay, Your application is submitted successfully
              </h2>
              <p className="text-sm text-muted-foreground max-w-md">
                Thank you for submitting your ATTENTION verse! We&apos;re excited to review your submission and will get back to you soon. Stay tuned and keep making music!
              </p>
            </div>
            {/* <Button
              onClick={handleClose}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8"
            >
              Done
            </Button> */}
          </div>
        ) : (
          <>
            {/* Header - Hidden on mobile, shown on desktop */}
            <div className="hidden sm:flex items-center justify-between p-6 border-b border-border sticky top-0 bg-background z-10">
              <div className="flex flex-col items-start gap-3">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground text-lg font-bold"><Disc/></span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-foreground">Apply for Open Verse Contest</h2>
                  <p className="text-sm text-muted-foreground">Upload your recording to SMASH and share it with the world.</p>
                </div>
              </div>
            </div>

            {/* Mobile Header - Compact version for mobile */}
            <div className="sm:hidden flex items-center justify-between p-4 border-b border-border sticky top-0 bg-background z-10">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-primary-foreground text-sm font-bold"><Disc/></span>
                </div>
                <div>
                  <h2 className="text-lg font-bold text-foreground">Apply for Open Verse Contest</h2>
                  <p className="text-xs text-muted-foreground">Upload your recording to SMASH and share it with the world.</p>
                </div>
              </div>
            </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Title Field */}
          <div className="flex flex-col justify-start items-start gap-1.5">
            <label className="text-sm font-bold font-lato text-foreground">
              Title *
            </label>
            <Input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Specify the title for submission"
              className="w-full px-4 py-2.5 bg-white rounded border border-border text-sm font-lato text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
            />
          </div>

          {/* Application Message */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Application Message</label>
            <Textarea
              placeholder="Tell the artist why you're perfect for this gig"
              value={applicationMessage}
              onChange={(e) => {
                if (e.target.value.length <= 1000) {
                  setApplicationMessage(e.target.value);
                }
              }}
              className="min-h-[80px] sm:min-h-[100px] resize-none border-input focus:border-ring focus:ring-ring"
              maxLength={1000}
            />
            <div className="text-xs text-muted-foreground text-right">
              {applicationMessage.length}/1000 characters
            </div>
          </div>

          {/* Upload Section */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Upload your Remix File</label>

            {/* Upload Area */}
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-4 sm:p-8 text-center transition-colors cursor-pointer",
                isDragOver
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              )}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center gap-3 sm:gap-4">
                <Upload className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-sm sm:text-base text-muted-foreground">
                    Drag & drop your audio file or click to browse
                  </p>
                  <Button
                    className="cursor-pointer bg-primary hover:bg-primary/90 text-primary-foreground px-4 sm:px-6 text-sm"
                  >
                    Select File
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    Supports: AIF, AIFF, WAV, MP3, MP4, AAC
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* File List */}
          {uploadFiles.length > 0 && (
            <div className="space-y-3">
              {uploadFiles.map((uploadFile) => {
                const duration = fileDurations.get(uploadFile.id);
                const isPlaying = playingFileId === uploadFile.id;

                return (
                  <div
                    key={uploadFile.id}
                    className="flex items-center gap-3 p-3 border border-border rounded-lg"
                  >
                    {/* File Icon */}
                    <div className="w-10 h-10 bg-primary rounded flex items-center justify-center flex-shrink-0">
                      <span className="text-primary-foreground text-xs font-bold">
                        {uploadFile.name.split('.').pop()?.toUpperCase()}
                      </span>
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-foreground truncate">
                          {uploadFile.name}
                        </h4>
                        <div className="flex items-center gap-2 ml-2">
                          <span className="text-xs text-muted-foreground">
                            {formatFileSize(uploadFile.file.size)}
                          </span>
                          {duration && (
                            <>
                              <span className="text-xs text-muted-foreground">•</span>
                              <span className="text-xs text-muted-foreground">
                                {formatDuration(duration)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-1">
                      {/* Play/Pause Button */}
                      {uploadFile.status === 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePlayPause(uploadFile.id, uploadFile.file)}
                          className="h-8 w-8 p-0 text-primary hover:text-primary/80"
                        >
                          {isPlaying ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                      )}

                      {/* Download Button */}
                      {uploadFile.status === 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(uploadFile)}
                          className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}

                      {/* Delete Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFile(uploadFile.id)}
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Errors */}
          {errors.length > 0 && (
            <div className="space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-destructive bg-destructive/10 border border-destructive/20 p-3 rounded-lg">
                  {error}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-center sm:justify-end gap-3 p-4 sm:p-6 border-t border-border bg-muted/30 sticky bottom-0 z-10">
          <Button
            variant="outline"
            onClick={handleClose}
            className="px-4 sm:px-6 flex-1 sm:flex-none"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 sm:px-6 flex-1 sm:flex-none"
          >
            Submit Track
          </Button>
        </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".aif,.aiff,.wav,.mp3,.mp4,.aac,audio/wav,audio/mpeg,audio/mp3,audio/aiff,audio/x-aiff,audio/mp4,audio/aac"
              onChange={handleFileSelect}
              className="hidden"
            />
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
