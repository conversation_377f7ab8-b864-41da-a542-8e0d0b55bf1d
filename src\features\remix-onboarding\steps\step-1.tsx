'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useRemixOnboarding, validateStep1 } from '../remix-onboarding-context';

export default function Step1() {
  const { data, updateData, nextStep } = useRemixOnboarding();
  const [realName, setRealName] = useState(data.realName);
  const [artistName, setArtistName] = useState(data.artistName);

  const handleContinue = () => {
    const updatedData = { realName, artistName };
    updateData(updatedData);
    
    if (validateStep1({ ...data, ...updatedData })) {
      nextStep();
    }
  };

  const isValid = realName.trim().length > 0 && artistName.trim().length > 0;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div>
        <h1 className="text-foreground text-3xl font-bold font-arvo leading-10">
          Tell us about you
        </h1>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Real Name Field */}
        <div className="space-y-2">
          <Label
            htmlFor="real-name"
            className="text-muted-foreground text-sm font-normal font-arvo "
          >
            Write your real name *
          </Label>
          <Input
            id="real-name"
            type="text"
            value={realName}
            onChange={(e) => setRealName(e.target.value)}
            placeholder="Ana Parker"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-lato font-semibold text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
        </div>

        {/* Artist Name Field */}
        <div className="space-y-2">
          <Label
            htmlFor="artist-name"
            className="text-muted-foreground text-sm font-normal font-arvo"
          >
            Write your artist name *
          </Label>
          <Input
            id="artist-name"
            type="text"
            value={artistName}
            onChange={(e) => setArtistName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && isValid) {
                handleContinue();
              }
            }}
            placeholder="Savvy Anna"
            className="w-full px-4 py-3 bg-card border border-border rounded text-sm font-lato font-semibold text-card-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-1 focus:ring-primary"
          />
          <p className="text-xs text-muted-foreground font-lato">
            This is the name your fans will see
          </p>
        </div>
      </div>

      {/* Continue Button */}
      <div className="pt-6">
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="cursor-pointer w-full h-12 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
